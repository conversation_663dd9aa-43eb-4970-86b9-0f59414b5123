import { Injectable } from '@nestjs/common';
import { MetricsService } from '@apply-goal-backend/monitoring';

@Injectable()
export class AppService {
  private requestCounter: any;
  private requestDuration: any;
  private gatewayOperationsCounter: any;
  private gatewayProcessingDuration: any;
  private activeConnectionsGauge: any;

  constructor(private metricsService: MetricsService) {
    // HTTP metrics
    this.requestCounter = this.metricsService.createCounter(
      'http_requests_total',
      'Total number of HTTP requests processed',
      ['method', 'endpoint', 'status']
    );

    this.requestDuration = this.metricsService.createHistogram(
      'http_request_duration_seconds',
      'HTTP request duration in seconds',
      ['method', 'endpoint']
    );

    // Gateway-specific metrics
    this.gatewayOperationsCounter = this.metricsService.createCounter(
      'gateway_operations_total',
      'Total number of gateway operations',
      ['operation', 'status']
    );

    this.gatewayProcessingDuration = this.metricsService.createHistogram(
      'gateway_processing_duration_seconds',
      'Gateway processing duration in seconds',
      ['operation'],
      [0.1, 0.5, 1, 2, 5, 10]
    );

    this.activeConnectionsGauge = this.metricsService.createGauge(
      'active_connections_current',
      'Current number of active connections',
      ['service']
    );
  }

  // Track HTTP request
  trackHttpRequest(
    method: string,
    endpoint: string,
    status: string,
    duration: number
  ) {
    this.requestCounter.inc({ method, endpoint, status });
    this.requestDuration.observe({ method, endpoint }, duration);
  }

  // Track gateway operation
  trackGatewayOperation(operation: string, status: string, duration: number) {
    this.gatewayOperationsCounter.inc({ operation, status });
    this.gatewayProcessingDuration.observe({ operation }, duration);
  }

  // Track active connections
  setActiveConnections(service: string, count: number) {
    this.activeConnectionsGauge.set({ service }, count);
  }

  getData(): { message: string } {
    return { message: 'Welcome to student-apigw!' };
  }
}
