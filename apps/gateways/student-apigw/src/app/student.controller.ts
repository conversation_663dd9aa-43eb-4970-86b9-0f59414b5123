import {
  Controller,
  Get,
  Post,
  Put,
  Delete,
  Body,
  Param,
  Query,
  Headers,
  UseGuards,
  HttpException,
  HttpStatus,
} from '@nestjs/common';
import { StudentClientService } from './student.service';
import {
  CurrentUser,
  Permissions,
  JwtAuthGuard,
  PermissionsGuard,
  Public,
} from '@apply-goal-backend/auth';

// Create a simple IP decorator
import { createParamDecorator, ExecutionContext } from '@nestjs/common';

const Ip = createParamDecorator(
  (data: unknown, ctx: ExecutionContext): string => {
    const request = ctx.switchToHttp().getRequest();
    return (
      request.ip ||
      request.connection.remoteAddress ||
      request.socket.remoteAddress ||
      (request.connection.socket
        ? request.connection.socket.remoteAddress
        : null) ||
      request.headers['x-forwarded-for'] ||
      request.headers['x-real-ip'] ||
      'unknown'
    );
  }
);

// Define permission constants (these should match your auth service)
const StudentPermissions = {
  STUDENT_PROFILE_VIEW: 'StudentProfileManagement:View',
  STUDENT_PROFILE_EDIT: 'StudentProfileManagement:Edit',
  STUDENT_PROFILE_CREATE: 'StudentProfileManagement:Create',
  STUDENT_PROFILE_DELETE: 'StudentProfileManagement:Delete',
  STUDENT_ACADEMIC_VIEW: 'StudentAcademic:View',
  STUDENT_ACADEMIC_EDIT: 'StudentAcademic:Edit',
  STUDENT_ENROLLMENT_MANAGE: 'StudentEnrollment:Manage',
} as const;

// Define interfaces for the new API
interface StudentPersonalInfoRequest {
  firstName: string;
  lastName: string;
  nameInNative: string;
  email: string;
  phone: string;
  guardianPhone: string;
  dateOfBirth: string;
  gender: string;
  fatherName: string;
  motherName: string;
  nid: string;
  passport: string;
  presentAddress: {
    address: string;
    country: string;
    state: string;
    city: string;
    postalCode: string;
  };
  permanentAddress: {
    address: string;
    country: string;
    state: string;
    city: string;
    postalCode: string;
  };
  maritalStatus: {
    status: string;
    spouseName: string;
    spousePhone: string;
    spousePassport: string;
  };
  sponsor: {
    name: string;
    relation: string;
    phone: string;
    email: string;
  };
  emergencyContact: {
    lastName: string;
    middleName: string;
    firstName: string;
    phoneHome: string;
    phoneMobile: string;
    relation: string;
  };
  preferredSubject: string[];
  preferredCountry: string[];
  socialLinks: Array<{
    platform: string;
    url: string;
  }>;
  reference: string;
  note: string;
}

@Controller('student')
@UseGuards(JwtAuthGuard, PermissionsGuard)
export class StudentController {
  constructor(private readonly studentService: StudentClientService) {}

  // New Student Personal Info API
  @Post()
  @Permissions(StudentPermissions.STUDENT_PROFILE_CREATE)
  async createStudentPersonalInfo(
    @CurrentUser('id') userId: number,
    @CurrentUser('organizationId') organizationId: string,
    @CurrentUser('roles') roles: string[],
    @Ip() ipAddress: string,
    @Headers('user-agent') userAgent: string,
    @Body() studentData: StudentPersonalInfoRequest
  ) {
    try {
      return await this.studentService.createStudentPersonalInfo(
        studentData,
        organizationId
      );
    } catch (error) {
      throw new HttpException(
        error.message || 'Failed to create student personal info',
        error.status || HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  @Put(':id')
  @Permissions(StudentPermissions.STUDENT_PROFILE_EDIT)
  async updateStudentPersonalInfo(
    @Param('id') id: string,
    @CurrentUser('id') userId: number,
    @CurrentUser('organizationId') organizationId: string,
    @CurrentUser('roles') roles: string[],
    @Ip() ipAddress: string,
    @Headers('user-agent') userAgent: string,
    @Body() studentData: StudentPersonalInfoRequest
  ) {
    try {
      return await this.studentService.updateStudentPersonalInfo(
        id,
        studentData,
        organizationId
      );
    } catch (error) {
      throw new HttpException(
        error.message || 'Failed to update student personal info',
        error.status || HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  // Legacy Student CRUD Operations (keeping for backward compatibility)
  @Post('legacy')
  @Permissions(StudentPermissions.STUDENT_PROFILE_CREATE)
  async createStudent(
    @CurrentUser('id') userId: number,
    @CurrentUser('roles') roles: string[],
    @Ip() ipAddress: string,
    @Headers('user-agent') userAgent: string,
    @Body()
    createStudentData: {
      first_name: string;
      last_name: string;
      email: string;
      phone: string;
      date_of_birth: {
        year: number;
        month: number;
        day: number;
      };
      major: string;
      minor?: string;
      address: {
        street: string;
        city: string;
        state: string;
        postal_code: string;
        country: string;
      };
      emergency_contacts?: Array<{
        name: string;
        relationship: string;
        phone: string;
        email: string;
      }>;
      metadata?: Record<string, string>;
    }
  ) {
    try {
      return await this.studentService.createStudent(createStudentData);
    } catch (error) {
      throw new HttpException(
        error.message || 'Failed to create student',
        error.status || HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  @Get(':id')
  @Permissions(StudentPermissions.STUDENT_PROFILE_VIEW)
  async getStudent(
    @Param('id') id: string,
    @CurrentUser('id') userId: number,
    @CurrentUser('roles') roles: string[],
    @Ip() ipAddress: string,
    @Headers('user-agent') userAgent: string
  ) {
    try {
      return await this.studentService.getStudent(id);
    } catch (error) {
      throw new HttpException(
        error.message || 'Failed to get student',
        error.status || HttpStatus.NOT_FOUND
      );
    }
  }

  @Put('legacy/:id')
  @Permissions(StudentPermissions.STUDENT_PROFILE_EDIT)
  async updateStudent(
    @Param('id') id: string,
    @CurrentUser('id') userId: number,
    @CurrentUser('roles') roles: string[],
    @Ip() ipAddress: string,
    @Headers('user-agent') userAgent: string,
    @Body() updateStudentData: any
  ) {
    try {
      return await this.studentService.updateStudent(id, updateStudentData);
    } catch (error) {
      throw new HttpException(
        error.message || 'Failed to update student',
        error.status || HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  @Delete(':id')
  @Permissions(StudentPermissions.STUDENT_PROFILE_DELETE)
  async deleteStudent(
    @Param('id') id: string,
    @CurrentUser('id') userId: number,
    @CurrentUser('roles') roles: string[],
    @Ip() ipAddress: string,
    @Headers('user-agent') userAgent: string
  ) {
    try {
      return await this.studentService.deleteStudent(id);
    } catch (error) {
      throw new HttpException(
        error.message || 'Failed to delete student',
        error.status || HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  @Get()
  @Permissions(StudentPermissions.STUDENT_PROFILE_VIEW)
  async listStudents(
    @CurrentUser('id') userId: number,
    @CurrentUser('roles') roles: string[],
    @Ip() ipAddress: string,
    @Headers('user-agent') userAgent: string,
    @Query('page_size') pageSize?: number,
    @Query('page_token') pageToken?: string,
    @Query('filter') filter?: string,
    @Query('order_by') orderBy?: string
  ) {
    try {
      const filters = {
        page_size: pageSize || 10,
        page_token: pageToken || '',
        filter: filter || '',
        order_by: orderBy || 'created_at desc',
      };
      return await this.studentService.listStudents(filters);
    } catch (error) {
      throw new HttpException(
        error.message || 'Failed to list students',
        error.status || HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  // Academic Operations
  @Post(':studentId/enrollments')
  @Permissions(StudentPermissions.STUDENT_ENROLLMENT_MANAGE)
  async enrollInCourse(
    @Param('studentId') studentId: string,
    @CurrentUser('id') userId: number,
    @CurrentUser('roles') roles: string[],
    @Ip() ipAddress: string,
    @Headers('user-agent') userAgent: string,
    @Body()
    enrollmentData: {
      course_id: string;
      semester: string;
    }
  ) {
    try {
      return await this.studentService.enrollInCourse(
        studentId,
        enrollmentData.course_id,
        enrollmentData.semester
      );
    } catch (error) {
      throw new HttpException(
        error.message || 'Failed to enroll in course',
        error.status || HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  @Delete(':studentId/enrollments/:courseId')
  @Permissions(StudentPermissions.STUDENT_ENROLLMENT_MANAGE)
  async dropCourse(
    @Param('studentId') studentId: string,
    @Param('courseId') courseId: string,
    @CurrentUser('id') userId: number,
    @CurrentUser('roles') roles: string[],
    @Ip() ipAddress: string,
    @Headers('user-agent') userAgent: string,
    @Query('semester') semester: string
  ) {
    try {
      return await this.studentService.dropCourse(
        studentId,
        courseId,
        semester
      );
    } catch (error) {
      throw new HttpException(
        error.message || 'Failed to drop course',
        error.status || HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  @Get(':studentId/enrollments')
  @Permissions(StudentPermissions.STUDENT_ACADEMIC_VIEW)
  async getEnrollments(
    @Param('studentId') studentId: string,
    @CurrentUser('id') userId: number,
    @CurrentUser('roles') roles: string[],
    @Ip() ipAddress: string,
    @Headers('user-agent') userAgent: string,
    @Query('semester') semester?: string
  ) {
    try {
      return await this.studentService.getEnrollments(studentId, semester);
    } catch (error) {
      throw new HttpException(
        error.message || 'Failed to get enrollments',
        error.status || HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  @Put(':studentId/grades')
  @Permissions(StudentPermissions.STUDENT_ACADEMIC_EDIT)
  async updateGrades(
    @Param('studentId') studentId: string,
    @CurrentUser('id') userId: number,
    @CurrentUser('roles') roles: string[],
    @Ip() ipAddress: string,
    @Headers('user-agent') userAgent: string,
    @Body()
    gradeData: {
      course_id: string;
      grade: string;
    }
  ) {
    try {
      return await this.studentService.updateGrades(
        studentId,
        gradeData.course_id,
        gradeData.grade
      );
    } catch (error) {
      throw new HttpException(
        error.message || 'Failed to update grades',
        error.status || HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  @Get(':studentId/academic-progress')
  @Permissions(StudentPermissions.STUDENT_ACADEMIC_VIEW)
  async getAcademicProgress(
    @Param('studentId') studentId: string,
    @CurrentUser('id') userId: number,
    @CurrentUser('roles') roles: string[],
    @Ip() ipAddress: string,
    @Headers('user-agent') userAgent: string
  ) {
    try {
      return await this.studentService.getAcademicProgress(studentId);
    } catch (error) {
      throw new HttpException(
        error.message || 'Failed to get academic progress',
        error.status || HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  @Get(':studentId/transcript')
  @Permissions(StudentPermissions.STUDENT_ACADEMIC_VIEW)
  async getTranscript(
    @Param('studentId') studentId: string,
    @CurrentUser('id') userId: number,
    @CurrentUser('roles') roles: string[],
    @Ip() ipAddress: string,
    @Headers('user-agent') userAgent: string
  ) {
    try {
      return await this.studentService.getTranscript(studentId);
    } catch (error) {
      throw new HttpException(
        error.message || 'Failed to get transcript',
        error.status || HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  // University Integration Endpoints
  @Get('universities/:universityId')
  @Permissions(StudentPermissions.STUDENT_PROFILE_VIEW)
  async getUniversity(
    @Param('universityId') universityId: string,
    @CurrentUser('id') userId: number,
    @CurrentUser('roles') roles: string[],
    @Ip() ipAddress: string,
    @Headers('user-agent') userAgent: string
  ) {
    try {
      return await this.studentService.getUniversity(universityId);
    } catch (error) {
      throw new HttpException(
        error.message || 'Failed to get university',
        error.status || HttpStatus.NOT_FOUND
      );
    }
  }

  @Get('universities')
  @Permissions(StudentPermissions.STUDENT_PROFILE_VIEW)
  async listUniversities(
    @CurrentUser('id') userId: number,
    @CurrentUser('roles') roles: string[],
    @Ip() ipAddress: string,
    @Headers('user-agent') userAgent: string,
    @Query('page_size') pageSize?: number,
    @Query('page_token') pageToken?: string,
    @Query('filter') filter?: string
  ) {
    try {
      const filters = {
        page_size: pageSize || 10,
        page_token: pageToken || '',
        filter: filter || '',
      };
      return await this.studentService.listUniversities(filters);
    } catch (error) {
      throw new HttpException(
        error.message || 'Failed to list universities',
        error.status || HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  // Agency Integration Endpoints
  @Get('agencies/:agencyId')
  @Permissions(StudentPermissions.STUDENT_PROFILE_VIEW)
  async getAgency(
    @Param('agencyId') agencyId: string,
    @CurrentUser('id') userId: number,
    @CurrentUser('roles') roles: string[],
    @Ip() ipAddress: string,
    @Headers('user-agent') userAgent: string
  ) {
    try {
      return await this.studentService.getAgency(agencyId);
    } catch (error) {
      throw new HttpException(
        error.message || 'Failed to get agency',
        error.status || HttpStatus.NOT_FOUND
      );
    }
  }

  @Get('agencies')
  @Permissions(StudentPermissions.STUDENT_PROFILE_VIEW)
  async listAgencies(
    @CurrentUser('id') userId: number,
    @CurrentUser('roles') roles: string[],
    @Ip() ipAddress: string,
    @Headers('user-agent') userAgent: string,
    @Query('page_size') pageSize?: number,
    @Query('page_token') pageToken?: string,
    @Query('filter') filter?: string
  ) {
    try {
      const filters = {
        page_size: pageSize || 10,
        page_token: pageToken || '',
        filter: filter || '',
      };
      return await this.studentService.listAgencies(filters);
    } catch (error) {
      throw new HttpException(
        error.message || 'Failed to list agencies',
        error.status || HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }
}
