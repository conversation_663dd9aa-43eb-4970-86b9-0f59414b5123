import { Injectable, Logger, OnModuleInit } from '@nestjs/common';
import {
  Client,
  ClientGrpc,
  RpcException,
  Transport,
} from '@nestjs/microservices';
import { join } from 'path';
import { firstValueFrom, Observable, catchError } from 'rxjs';

// Student Service interfaces
interface StudentService {
  createStudent(data: any): Observable<any>;
  getStudent(data: any): Observable<any>;
  updateStudent(data: any): Observable<any>;
  deleteStudent(data: any): Observable<any>;
  listStudents(data: any): Observable<any>;
  enrollInCourse(data: any): Observable<any>;
  dropCourse(data: any): Observable<any>;
  getEnrollments(data: any): Observable<any>;
  updateGrades(data: any): Observable<any>;
  getAcademicProgress(data: any): Observable<any>;
  getTranscript(data: any): Observable<any>;
}

// Auth Service interfaces
interface AuthService {
  validateToken(data: any): Observable<any>;
  getUserById(data: any): Observable<any>;
  getUserRoles(data: any): Observable<any>;
  register(data: any): Observable<any>;
}

// University Service interfaces
interface UniversityService {
  getUniversity(data: any): Observable<any>;
  listUniversities(data: any): Observable<any>;
  getCourses(data: any): Observable<any>;
  getPrograms(data: any): Observable<any>;
}

// Agency Service interfaces
interface AgencyService {
  getAgency(data: any): Observable<any>;
  listAgencies(data: any): Observable<any>;
  getAgencyStudents(data: any): Observable<any>;
}

@Injectable()
export class StudentClientService implements OnModuleInit {
  private readonly logger = new Logger(StudentClientService.name);

  private studentService: StudentService;
  private authService: AuthService;
  private universityService: UniversityService;
  private agencyService: AgencyService;

  // gRPC Clients
  @Client({
    transport: Transport.GRPC,
    options: {
      package: 'students',
      protoPath: join(
        process.cwd(),
        'libs/shared/dto/src/lib/students/students.proto'
      ),
      url: process.env.STUDENTS_SERVICE_URL || 'localhost:50058',
    },
  })
  private studentsClient: ClientGrpc;

  @Client({
    transport: Transport.GRPC,
    options: {
      package: 'auth',
      protoPath: join(process.cwd(), 'libs/shared/dto/src/lib/auth/auth.proto'),
      url: process.env.AUTH_SERVICE_URL || 'localhost:50051',
    },
  })
  private authClient: ClientGrpc;

  @Client({
    transport: Transport.GRPC,
    options: {
      package: 'university',
      protoPath: join(
        process.cwd(),
        'libs/shared/dto/src/lib/university/university.proto'
      ),
      url: process.env.UNIVERSITY_SERVICE_URL || 'localhost:50059',
    },
  })
  private universityClient: ClientGrpc;

  @Client({
    transport: Transport.GRPC,
    options: {
      package: 'agency',
      protoPath: join(
        process.cwd(),
        'libs/shared/dto/src/lib/agency/agency.proto'
      ),
      url: process.env.AGENCY_SERVICE_URL || 'localhost:50060',
    },
  })
  private agencyClient: ClientGrpc;

  onModuleInit() {
    this.studentService =
      this.studentsClient.getService<StudentService>('StudentService');
    this.authService = this.authClient.getService<AuthService>('AuthService');
    this.universityService =
      this.universityClient.getService<UniversityService>('UniversityService');
    this.agencyService =
      this.agencyClient.getService<AgencyService>('AgencyService');
  }

  // New Student Personal Info Methods
  async createStudentPersonalInfo(
    studentData: any,
    organizationId: string
  ): Promise<any> {
    try {
      // First check if user exists by email
      const userExists = await this.checkUserExists(studentData.email);

      if (!userExists) {
        // Create user first
        await this.createUserForStudent(studentData, organizationId);
      }

      // Transform the request data to match the proto format
      const protoData = this.transformToProtoFormat(studentData);

      return await firstValueFrom(
        this.studentService.createStudent(protoData).pipe(
          catchError((error) => {
            this.logger.error('Error creating student personal info:', error);
            throw new RpcException(error);
          })
        )
      );
    } catch (error) {
      this.logger.error('Failed to create student personal info:', error);
      throw error;
    }
  }

  async updateStudentPersonalInfo(
    id: string,
    studentData: any,
    organizationId: string
  ): Promise<any> {
    try {
      // Check if user exists by email, if not create one
      const userExists = await this.checkUserExists(studentData.email);

      if (!userExists) {
        // Create user first
        await this.createUserForStudent(studentData, organizationId);
      }

      // Transform the request data to match the proto format
      const protoData = this.transformToProtoFormat(studentData);

      return await firstValueFrom(
        this.studentService.updateStudent({ id, payload: protoData }).pipe(
          catchError((error) => {
            this.logger.error('Error updating student personal info:', error);
            throw new RpcException(error);
          })
        )
      );
    } catch (error) {
      this.logger.error('Failed to update student personal info:', error);
      throw error;
    }
  }

  // Helper method to check if user exists
  private async checkUserExists(email: string): Promise<boolean> {
    try {
      const result = await firstValueFrom(
        this.authService.getUserById({ email }).pipe(
          catchError((error) => {
            // If user not found, return false
            if (error.code === 5 || error.message?.includes('not found')) {
              return Promise.resolve({ success: false });
            }
            throw error;
          })
        )
      );
      return result.success !== false;
    } catch (error) {
      this.logger.debug('User does not exist:', email);
      return false;
    }
  }

  // Helper method to create user for student
  private async createUserForStudent(
    studentData: any,
    organizationId: string
  ): Promise<any> {
    try {
      const registerData = {
        name: `${studentData.firstName} ${studentData.lastName}`,
        email: studentData.email,
        password: 'TempPassword123!', // Temporary password
        phone: studentData.phone,
        nationality: '', // Can be derived from country if needed
        organizationName: '', // Will be handled by organizationId
        roleName: 'Student', // Default role for students
        departmentName: 'Students', // Default department
        ipAddress: '127.0.0.1',
        userAgent: 'Student Registration System',
      };

      return await firstValueFrom(
        this.authService.register(registerData).pipe(
          catchError((error) => {
            this.logger.error('Error creating user for student:', error);
            throw new RpcException(error);
          })
        )
      );
    } catch (error) {
      this.logger.error('Failed to create user for student:', error);
      throw error;
    }
  }

  // Helper method to transform request data to proto format
  private transformToProtoFormat(studentData: any): any {
    return {
      name: `${studentData.firstName} ${studentData.lastName}`,
      first_name: studentData.firstName,
      native_name: studentData.nameInNative,
      email: studentData.email,
      date_of_birth: studentData.dateOfBirth,
      gender: studentData.gender,
      father_name: studentData.fatherName,
      mother_name: studentData.motherName,
      national_id: studentData.nid,
      passport_number: studentData.passport,
      marital_status: studentData.maritalStatus?.status || '',
      spouse_name: studentData.maritalStatus?.spouseName || '',
      spouse_passport_number: studentData.maritalStatus?.spousePassport || '',

      // Present Address
      present_address: studentData.presentAddress?.address || '',
      present_country: studentData.presentAddress?.country || '',
      present_state: studentData.presentAddress?.state || '',
      present_city: studentData.presentAddress?.city || '',
      present_postal_code: studentData.presentAddress?.postalCode || '',

      // Permanent Address
      permanent_address: studentData.permanentAddress?.address || '',
      permanent_country: studentData.permanentAddress?.country || '',
      permanent_state: studentData.permanentAddress?.state || '',
      permanent_city: studentData.permanentAddress?.city || '',
      permanent_postal_code: studentData.permanentAddress?.postalCode || '',

      // Sponsor & Guardian
      sponsor_name: studentData.sponsor?.name || '',
      relationship: studentData.sponsor?.relation || '',
      phone_number: studentData.phone,
      guardian_number: studentData.guardianPhone,

      // Preferences
      preferred_subjects: studentData.preferredSubject || [],
      preferred_countries: studentData.preferredCountry || [],

      // Social links
      social_links: (studentData.socialLinks || []).map((link: any) => ({
        title: link.platform,
        url: link.url,
      })),

      reference: studentData.reference || '',
      note: studentData.note || '',
    };
  }

  // Student Service Methods (Legacy)
  async createStudent(studentData: any): Promise<any> {
    try {
      return await firstValueFrom(
        this.studentService.createStudent(studentData).pipe(
          catchError((error) => {
            this.logger.error('Error creating student:', error);
            throw new RpcException(error);
          })
        )
      );
    } catch (error) {
      this.logger.error('Failed to create student:', error);
      throw error;
    }
  }

  async getStudent(id: string): Promise<any> {
    try {
      return await firstValueFrom(
        this.studentService.getStudent({ id }).pipe(
          catchError((error) => {
            this.logger.error('Error getting student:', error);
            throw new RpcException(error);
          })
        )
      );
    } catch (error) {
      this.logger.error('Failed to get student:', error);
      throw error;
    }
  }

  async updateStudent(id: string, studentData: any): Promise<any> {
    try {
      return await firstValueFrom(
        this.studentService.updateStudent({ id, student: studentData }).pipe(
          catchError((error) => {
            this.logger.error('Error updating student:', error);
            throw new RpcException(error);
          })
        )
      );
    } catch (error) {
      this.logger.error('Failed to update student:', error);
      throw error;
    }
  }

  async deleteStudent(id: string): Promise<any> {
    try {
      return await firstValueFrom(
        this.studentService.deleteStudent({ id }).pipe(
          catchError((error) => {
            this.logger.error('Error deleting student:', error);
            throw new RpcException(error);
          })
        )
      );
    } catch (error) {
      this.logger.error('Failed to delete student:', error);
      throw error;
    }
  }

  async listStudents(filters: any): Promise<any> {
    try {
      return await firstValueFrom(
        this.studentService.listStudents(filters).pipe(
          catchError((error) => {
            this.logger.error('Error listing students:', error);
            throw new RpcException(error);
          })
        )
      );
    } catch (error) {
      this.logger.error('Failed to list students:', error);
      throw error;
    }
  }

  // Academic Operations
  async enrollInCourse(
    studentId: string,
    courseId: string,
    semester: string
  ): Promise<any> {
    try {
      return await firstValueFrom(
        this.studentService
          .enrollInCourse({
            student_id: studentId,
            course_id: courseId,
            semester,
          })
          .pipe(
            catchError((error) => {
              this.logger.error('Error enrolling in course:', error);
              throw new RpcException(error);
            })
          )
      );
    } catch (error) {
      this.logger.error('Failed to enroll in course:', error);
      throw error;
    }
  }

  async dropCourse(
    studentId: string,
    courseId: string,
    semester: string
  ): Promise<any> {
    try {
      return await firstValueFrom(
        this.studentService
          .dropCourse({ student_id: studentId, course_id: courseId, semester })
          .pipe(
            catchError((error) => {
              this.logger.error('Error dropping course:', error);
              throw new RpcException(error);
            })
          )
      );
    } catch (error) {
      this.logger.error('Failed to drop course:', error);
      throw error;
    }
  }

  async getEnrollments(studentId: string, semester?: string): Promise<any> {
    try {
      return await firstValueFrom(
        this.studentService
          .getEnrollments({ student_id: studentId, semester })
          .pipe(
            catchError((error) => {
              this.logger.error('Error getting enrollments:', error);
              throw new RpcException(error);
            })
          )
      );
    } catch (error) {
      this.logger.error('Failed to get enrollments:', error);
      throw error;
    }
  }

  async updateGrades(
    studentId: string,
    courseId: string,
    grade: string
  ): Promise<any> {
    try {
      return await firstValueFrom(
        this.studentService
          .updateGrades({ student_id: studentId, course_id: courseId, grade })
          .pipe(
            catchError((error) => {
              this.logger.error('Error updating grades:', error);
              throw new RpcException(error);
            })
          )
      );
    } catch (error) {
      this.logger.error('Failed to update grades:', error);
      throw error;
    }
  }

  async getAcademicProgress(studentId: string): Promise<any> {
    try {
      return await firstValueFrom(
        this.studentService.getAcademicProgress({ student_id: studentId }).pipe(
          catchError((error) => {
            this.logger.error('Error getting academic progress:', error);
            throw new RpcException(error);
          })
        )
      );
    } catch (error) {
      this.logger.error('Failed to get academic progress:', error);
      throw error;
    }
  }

  async getTranscript(studentId: string): Promise<any> {
    try {
      return await firstValueFrom(
        this.studentService.getTranscript({ student_id: studentId }).pipe(
          catchError((error) => {
            this.logger.error('Error getting transcript:', error);
            throw new RpcException(error);
          })
        )
      );
    } catch (error) {
      this.logger.error('Failed to get transcript:', error);
      throw error;
    }
  }

  // Auth Service Methods
  async validateToken(token: string): Promise<any> {
    try {
      return await firstValueFrom(
        this.authService.validateToken({ token }).pipe(
          catchError((error) => {
            this.logger.error('Error validating token:', error);
            throw new RpcException(error);
          })
        )
      );
    } catch (error) {
      this.logger.error('Failed to validate token:', error);
      throw error;
    }
  }

  async getUserById(userId: string): Promise<any> {
    try {
      return await firstValueFrom(
        this.authService.getUserById({ id: userId }).pipe(
          catchError((error) => {
            this.logger.error('Error getting user by ID:', error);
            throw new RpcException(error);
          })
        )
      );
    } catch (error) {
      this.logger.error('Failed to get user by ID:', error);
      throw error;
    }
  }

  // University Service Methods
  async getUniversity(universityId: string): Promise<any> {
    try {
      return await firstValueFrom(
        this.universityService.getUniversity({ id: universityId }).pipe(
          catchError((error) => {
            this.logger.error('Error getting university:', error);
            throw new RpcException(error);
          })
        )
      );
    } catch (error) {
      this.logger.error('Failed to get university:', error);
      throw error;
    }
  }

  async listUniversities(filters: any): Promise<any> {
    try {
      return await firstValueFrom(
        this.universityService.listUniversities(filters).pipe(
          catchError((error) => {
            this.logger.error('Error listing universities:', error);
            throw new RpcException(error);
          })
        )
      );
    } catch (error) {
      this.logger.error('Failed to list universities:', error);
      throw error;
    }
  }

  // Agency Service Methods
  async getAgency(agencyId: string): Promise<any> {
    try {
      return await firstValueFrom(
        this.agencyService.getAgency({ id: agencyId }).pipe(
          catchError((error) => {
            this.logger.error('Error getting agency:', error);
            throw new RpcException(error);
          })
        )
      );
    } catch (error) {
      this.logger.error('Failed to get agency:', error);
      throw error;
    }
  }

  async listAgencies(filters: any): Promise<any> {
    try {
      return await firstValueFrom(
        this.agencyService.listAgencies(filters).pipe(
          catchError((error) => {
            this.logger.error('Error listing agencies:', error);
            throw new RpcException(error);
          })
        )
      );
    } catch (error) {
      this.logger.error('Failed to list agencies:', error);
      throw error;
    }
  }
}
